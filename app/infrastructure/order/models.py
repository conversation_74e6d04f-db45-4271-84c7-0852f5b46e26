from datetime import datetime

from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.extensions import db


class OrderModel(db.Model):
    """订单数据模型"""
    __tablename__ = 'orders'
    
    id = Column(db.Integer, primary_key=True)
    order_no = Column(String(36), nullable=True, unique=True, index=True, comment='订单号')
    user_id = Column(Integer, nullable=False, index=True, comment='用户ID')
    order_type = Column(String(20), nullable=False, default='normal', comment='订单类型')
    status = Column(String(20), nullable=False, index=True, comment='订单状态')
    total_amount = Column(Numeric(10, 2), nullable=False, comment='订单总金额')
    original_amount = Column(Numeric(10, 2), nullable=False, comment='订单原始金额')
    total_refunded = Column(Numeric(10, 2), nullable=False, default=0, comment='累计退款金额')
    
    # 优惠券ID列表
    coupon_ids = Column(JSON, nullable=True, comment='使用的优惠券ID列表')
    
    # 地址信息（JSON格式）
    shipping_address = Column(JSON, nullable=True, comment='收货地址')
    
    # 支付信息（JSON格式）
    payment_info = Column(JSON, nullable=True, comment='支付信息')
    
    # 备注
    remark = Column(String(500), nullable=True, comment='订单备注')
    
    # 操作日志（JSON格式）
    operation_logs = Column(JSON, nullable=True, comment='操作日志')
    
    # 时间信息
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    paid_at = Column(DateTime, nullable=True, comment='支付时间')
    cancelled_at = Column(DateTime, nullable=True, comment='取消时间')
    completed_at = Column(DateTime, nullable=True, comment='完成时间')
    expire_at = Column(DateTime, nullable=True, comment='过期时间')
    # 软删除时间
    deleted_at = Column(DateTime, nullable=True, comment='删除时间')
    
    # 取消原因
    cancel_reason = Column(String(50), nullable=True, comment='取消原因')
    
    # 乐观锁版本号
    version = Column(Integer, nullable=False, default=1, comment='版本号')
    
    # 关联关系
    items = relationship('OrderItemModel', back_populates='order', cascade='all, delete-orphan')
    sub_orders = relationship('SubOrderModel', back_populates='order', cascade='all, delete-orphan')


class OrderItemModel(db.Model):
    """订单项数据模型"""
    __tablename__ = 'order_items'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='订单项ID')
    order_id = Column(db.Integer, ForeignKey('orders.id'), nullable=False, index=True, comment='订单ID')
    sku_id = Column(Integer, nullable=False, index=True, comment='SKU ID')
    quantity = Column(Integer, nullable=False, comment='数量')
    unit_price = Column(Numeric(10, 2), nullable=False, comment='单价')
    original_price = Column(Numeric(10, 2), nullable=False, comment='原价')
    postage = Column(Numeric(10, 2), nullable=False, comment='邮费')
    points_ratio = Column(Integer, nullable=False, comment='积分比例')
    group_price = Column(Numeric(10, 2), nullable=False, comment='团员价')
    
    # 软删除时间
    deleted_at = Column(DateTime, nullable=True, comment='删除时间')
    
    # 关联关系
    order = relationship('OrderModel', back_populates='items')


class SubOrderModel(db.Model):
    """子订单数据模型"""
    __tablename__ = 'sub_orders'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='子订单ID(自增)')
    order_no = Column(String(40), nullable=True, unique=True, index=True, comment='子订单号(格式: 订单号-1,-2...)')
    order_id = Column(db.Integer, ForeignKey('orders.id'), nullable=False, index=True, comment='父订单ID')
    sku_id = Column(Integer, nullable=False, index=True, comment='SKU ID')
    quantity = Column(Integer, nullable=False, comment='数量')
    unit_price = Column(Numeric(10, 2), nullable=False, comment='单价')
    actual_payment = Column(Numeric(10, 2), nullable=False, default=0, comment='实际支付金额')
    status = Column(String(30), nullable=False, index=True, comment='子订单状态')

    # 物流信息字段
    logistics_company = Column(String(50), nullable=True, index=True, comment='物流公司')
    tracking_number = Column(String(50), nullable=True, index=True, comment='物流单号')
    shipping_time = Column(DateTime, nullable=True, comment='发货时间')
    salt = Column(String(16), nullable=True, comment='随机盐')
    # 物流动态信息（JSON格式）
    tracking_details = Column(JSON, nullable=True, comment='物流跟踪详情')
    
    # 退款信息（JSON格式）
    refund_info = Column(JSON, nullable=True, comment='退款信息')

    # 售后信息（JSON格式）
    after_sale_info = Column(JSON, nullable=True, comment='售后信息')
    
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    delivered_at = Column(DateTime, nullable=True, comment='收货时间')
    completed_at = Column(DateTime, nullable=True, comment='完成时间')
    # 软删除时间
    deleted_at = Column(DateTime, nullable=True, comment='删除时间')
    
    # 关联关系
    order = relationship('OrderModel', back_populates='sub_orders')
